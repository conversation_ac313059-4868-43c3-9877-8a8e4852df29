// Import Prisma enums directly as the single source of truth
import {
  ProductType,
  ProductCategory,
  MedicineClassification,
  UnitType
} from '@prisma/client';

// Re-export for convenience
export {
  ProductType,
  ProductCategory,
  MedicineClassification,
  UnitType
};

// Base interfaces
export interface ProductUnit {
  id: string;
  name: string;
  abbreviation: string;
  type: UnitType;
  isBaseUnit: boolean;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ProductUnitHierarchy {
  id: string;
  productId: string;
  unitId: string;
  parentUnitId?: string;
  conversionFactor: number;
  level: number;
  sellingPrice?: number;
  costPrice?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  unit: ProductUnit;
  parentUnit?: ProductUnitHierarchy;
  childUnits?: ProductUnitHierarchy[];
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export interface Product {
  id: string;
  code: string;
  name: string;
  genericName?: string;
  type: ProductType;
  category: ProductCategory;
  manufacturer?: string;

  // Indonesian Pharmacy Specific Fields
  bpomNumber?: string;
  medicineClassification: MedicineClassification;
  regulatorySymbol?: string;

  // Unit and Pricing Information
  baseUnitId: string;
  minimumStock?: number;
  maximumStock?: number;
  reorderPoint?: number;

  // Product Information
  description?: string;
  activeIngredient?: string;
  strength?: string;
  dosageForm?: string;
  indication?: string;
  contraindication?: string;
  sideEffects?: string;
  dosage?: string;
  storage?: string;

  // Metadata
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;

  // Relations
  baseUnit: ProductUnit;
  unitHierarchies: ProductUnitHierarchy[];
  createdByUser?: User;
  updatedByUser?: User;
}

// DTO interfaces
export interface CreateProductUnitHierarchyDto {
  unitId: string;
  parentUnitId?: string;
  conversionFactor: number;
  level: number;
  sellingPrice?: number;
  costPrice?: number;
}

export interface CreateProductDto {
  code: string;
  name: string;
  genericName?: string;
  type: ProductType;
  category: ProductCategory;
  manufacturer?: string;
  bpomNumber?: string;
  medicineClassification: MedicineClassification;
  regulatorySymbol?: string;
  baseUnitId: string;
  minimumStock?: number;
  maximumStock?: number;
  reorderPoint?: number;
  description?: string;
  activeIngredient?: string;
  strength?: string;
  dosageForm?: string;
  indication?: string;
  contraindication?: string;
  sideEffects?: string;
  dosage?: string;
  storage?: string;
  notes?: string;
  unitHierarchies?: CreateProductUnitHierarchyDto[];
}

export interface UpdateProductUnitHierarchyDto extends CreateProductUnitHierarchyDto {
  id?: string;
  isActive?: boolean;
}

export interface UpdateProductDto extends Partial<CreateProductDto> {
  isActive?: boolean;
  unitHierarchies?: UpdateProductUnitHierarchyDto[];
}

export interface ProductQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: ProductType;
  category?: ProductCategory;
  medicineClassification?: MedicineClassification;
  manufacturer?: string;
  isActive?: boolean;
  baseUnitId?: string;
  genericName?: string;
  bpomNumber?: string;
  activeIngredient?: string;
  dosageForm?: string;
  strength?: string;
  createdBy?: string;
  dateFrom?: string;
  dateTo?: string;
  lowStock?: boolean;
  outOfStock?: boolean;
  overStock?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ProductListResponse {
  data: Product[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface ProductStats {
  total: number;
  active: number;
  inactive: number;
  byType: Record<ProductType, number>;
  byCategory: Record<ProductCategory, number>;
  byMedicineClassification: Record<MedicineClassification, number>;
}
