'use client';

import { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useImmer } from 'use-immer';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Plus, Trash2, Calculator, Package, CreditCard, FileText, Edit3 } from 'lucide-react';
import { SupplierSelector } from '@/components/ui/supplier-selector';
import { LiveCurrencyInput } from '@/components/ui/currency-input';
import {
  PurchaseOrderFormData,
  CreatePurchaseOrderDto,
  PaymentMethod
} from '@/types/purchase-order';
import { useProduct } from '@/hooks/useProducts';
import { usePPNConfiguration } from '@/hooks/useTax';
import { formatCurrency } from '@/lib/utils';
import {
  PAYMENT_METHOD_OPTIONS,
  DISCOUNT_TYPE_OPTIONS,
  DEFAULT_PURCHASE_ORDER_FORM
} from '@/lib/constants/purchase-order';
import { PurchaseOrderItemDialog } from './purchase-order-item-dialog';

// Form validation schema - Handle null values from database
const purchaseOrderSchema = z.object({
  supplierId: z.string().min(1, 'Supplier harus dipilih'),
  orderDate: z.string().min(1, 'Tanggal order harus diisi'),
  expectedDelivery: z.string().optional().or(z.literal('')).or(z.null()),
  discountType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']).optional().or(z.null()),
  discountValue: z.number().min(0).optional().or(z.null()),
  paymentTerms: z.number().min(0).max(365).optional().or(z.null()),
  paymentMethod: z.nativeEnum(PaymentMethod).optional().or(z.null()),
  deliveryAddress: z.string().optional().or(z.null()),
  deliveryContact: z.string().optional().or(z.null()),
  deliveryPhone: z.string().optional().or(z.null()),
  deliveryNotes: z.string().optional().or(z.null()),
  notes: z.string().optional().or(z.null()),
  internalNotes: z.string().optional().or(z.null()),
  items: z.array(z.object({
    productId: z.string().min(1, 'Produk harus dipilih'),
    unitId: z.string().min(1, 'Satuan harus dipilih'),
    quantityOrdered: z.number().min(1, 'Kuantitas minimal 1'),
    unitPrice: z.number().min(0, 'Harga tidak boleh negatif'),
    discountType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']).optional().or(z.null()),
    discountValue: z.number().min(0).optional().or(z.null()),
    expectedDelivery: z.string().optional().or(z.literal('')).or(z.null()),
    qualitySpecs: z.string().optional().or(z.null()),
    notes: z.string().optional().or(z.null()),
  })).min(1, 'Minimal harus ada 1 item'),
});

type PurchaseOrderFormValues = z.infer<typeof purchaseOrderSchema>;

interface PurchaseOrderFormProps {
  initialData?: Partial<PurchaseOrderFormData>;
  onSubmit: (data: CreatePurchaseOrderDto) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
  submitLabel?: string;
}

export function PurchaseOrderForm({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  submitLabel = 'Simpan Purchase Order',
}: PurchaseOrderFormProps) {
  const [formState, updateFormState] = useImmer({
    subtotal: 0,
    discountAmount: 0,
    taxAmount: 0,
    totalAmount: 0,
  });

  const form = useForm<PurchaseOrderFormValues>({
    resolver: zodResolver(purchaseOrderSchema),
    defaultValues: {
      ...DEFAULT_PURCHASE_ORDER_FORM,
      ...initialData,
      items: initialData?.items || [
        {
          productId: '',
          unitId: '',
          quantityOrdered: 1,
          unitPrice: 0,
          discountType: undefined,
          discountValue: undefined,
          expectedDelivery: '',
          qualitySpecs: '',
          notes: '',
        }
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  const watchedItems = form.watch('items');
  const watchedDiscountType = form.watch('discountType');
  const watchedDiscountValue = form.watch('discountValue');

  // Handler for order-level percentage discount
  const handleOrderPercentageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Allow empty string for clearing the field
    if (inputValue === '') {
      form.setValue('discountValue', undefined);
      return;
    }

    const value = parseFloat(inputValue);

    // Validate percentage range (0-100)
    if (!isNaN(value) && value >= 0 && value <= 100) {
      form.setValue('discountValue', value);
    }
    // If invalid, don't update the form value but allow the input to show the typed value
  };

  // Get current PPN configuration
  const { data: ppnConfig, isLoading: isPPNLoading } = usePPNConfiguration();

  // Item dialog state
  const [isItemDialogOpen, setIsItemDialogOpen] = useState(false);
  const [editingItemIndex, setEditingItemIndex] = useState<number | null>(null);

  // Calculate totals whenever items or discounts change
  useEffect(() => {
    updateFormState(draft => {
      // Calculate subtotal from all items
      draft.subtotal = watchedItems.reduce((sum, item) => {
        const itemTotal = (item.quantityOrdered || 0) * (item.unitPrice || 0);
        const itemDiscountAmount = calculateItemDiscount(item);
        return sum + (itemTotal - itemDiscountAmount);
      }, 0);

      // Calculate order-level discount
      draft.discountAmount = calculateOrderDiscount(draft.subtotal, watchedDiscountType, watchedDiscountValue);

      // Calculate tax (PPN) using dynamic rate from backend
      const taxableAmount = draft.subtotal - draft.discountAmount;
      const taxRate = ppnConfig?.isActive ? (ppnConfig.taxRate / 100) : 0;
      draft.taxAmount = taxableAmount * taxRate;

      // Calculate total
      draft.totalAmount = taxableAmount + draft.taxAmount;
    });
  }, [watchedItems, watchedDiscountType, watchedDiscountValue, updateFormState, ppnConfig]);

  const calculateItemDiscount = (item: any): number => {
    if (!item.discountType || !item.discountValue || item.discountType === null || item.discountValue === null) return 0;

    const itemTotal = (item.quantityOrdered || 0) * (item.unitPrice || 0);

    if (item.discountType === 'PERCENTAGE') {
      return itemTotal * (item.discountValue / 100);
    } else {
      return Math.min(item.discountValue, itemTotal);
    }
  };

  const calculateOrderDiscount = (subtotal: number, discountType?: string | null, discountValue?: number | null): number => {
    if (!discountType || !discountValue || discountType === null || discountValue === null) return 0;

    if (discountType === 'PERCENTAGE') {
      return subtotal * (discountValue / 100);
    } else {
      return Math.min(discountValue, subtotal);
    }
  };

  const addItem = () => {
    setEditingItemIndex(null); // Clear editing index for new item
    setIsItemDialogOpen(true);
  };

  const editItem = (index: number) => {
    setEditingItemIndex(index);
    setIsItemDialogOpen(true);
  };

  const removeItem = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const handleSubmit = (data: PurchaseOrderFormValues) => {
    // Helper function to convert null to undefined
    const nullToUndefined = (value: any): any => {
      return value === null ? undefined : value;
    };

    // Convert date strings to ISO format if they exist
    const formatDateToISO = (dateStr: string | null | undefined): string | undefined => {
      if (!dateStr || dateStr === '') return undefined;
      try {
        // If it's already in YYYY-MM-DD format, convert to ISO
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
          return undefined;
        }
        return date.toISOString();
      } catch (error) {
        return undefined;
      }
    };

    const submitData: CreatePurchaseOrderDto & {
      taxAmount?: number;
      autoCalculateTax?: boolean;
    } = {
      supplierId: data.supplierId,
      orderDate: formatDateToISO(data.orderDate),
      expectedDelivery: formatDateToISO(nullToUndefined(data.expectedDelivery)),
      discountType: nullToUndefined(data.discountType),
      discountValue: data.discountValue ? Math.round(data.discountValue * 100) / 100 : undefined,
      paymentTerms: nullToUndefined(data.paymentTerms),
      paymentMethod: nullToUndefined(data.paymentMethod),
      deliveryAddress: nullToUndefined(data.deliveryAddress),
      deliveryContact: nullToUndefined(data.deliveryContact),
      deliveryPhone: nullToUndefined(data.deliveryPhone),
      deliveryNotes: nullToUndefined(data.deliveryNotes),
      notes: nullToUndefined(data.notes),
      internalNotes: nullToUndefined(data.internalNotes),
      // Tax information
      taxAmount: Math.max(0, Math.round(formState.taxAmount * 100) / 100), // Ensure non-negative and 2 decimal places
      autoCalculateTax: true, // We're using dynamic tax calculation
      items: data.items.map(item => ({
        productId: item.productId,
        unitId: item.unitId,
        quantityOrdered: item.quantityOrdered,
        unitPrice: Math.round(item.unitPrice * 100) / 100, // Round to 2 decimal places
        discountType: nullToUndefined(item.discountType),
        discountValue: item.discountValue ? Math.round(item.discountValue * 100) / 100 : undefined,
        expectedDelivery: formatDateToISO(nullToUndefined(item.expectedDelivery)),
        qualitySpecs: nullToUndefined(item.qualitySpecs),
        notes: nullToUndefined(item.notes),
      })),
    };

    onSubmit(submitData);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="h-full flex flex-col">
        {/* Main Content - Resizable Layout */}
        <div className="flex-1 min-h-0 overflow-hidden border rounded">
          <ResizablePanelGroup direction="horizontal" className="h-full">
            {/* Left Panel - Items Table */}
            <ResizablePanel defaultSize={65} minSize={40} className="min-w-0">
              <div className="h-full flex flex-col">
                {/* Items Header */}
                <div className="flex-none p-3 border-b bg-background">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Item Purchase Order</h3>
                      <p className="text-sm text-muted-foreground">
                        Tambahkan produk yang akan dipesan
                      </p>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addItem}
                      disabled={isSubmitting}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Tambah Item
                    </Button>
                  </div>
                </div>

                {/* Items Table */}
                <div className="flex-1 min-h-0 overflow-hidden">
                  <ScrollArea className="h-full">
                    <Table>
                      <TableHeader className="sticky top-0 bg-background">
                        <TableRow>
                          <TableHead className="w-[30%]">Produk</TableHead>
                          <TableHead className="w-[20%]">Kuantitas</TableHead>
                          <TableHead className="w-[15%] text-right">Harga</TableHead>
                          <TableHead className="w-[15%]">Diskon</TableHead>
                          <TableHead className="w-[15%] text-right">Total</TableHead>
                          <TableHead className="w-[5%]">Aksi</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {fields.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center h-24 text-muted-foreground">
                              Belum ada item. Klik "Tambah Item" untuk memulai.
                            </TableCell>
                          </TableRow>
                        ) : (
                          fields.map((field, index) => (
                            <PurchaseOrderItemDisplayRow
                              key={field.id}
                              index={index}
                              form={form}
                              onEdit={() => editItem(index)}
                              onRemove={() => removeItem(index)}
                              canRemove={fields.length > 1}
                              disabled={isSubmitting}
                            />
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </ScrollArea>
                </div>
              </div>
            </ResizablePanel>

            <ResizableHandle withHandle />

            {/* Right Panel - Order Summary & Controls */}
            <ResizablePanel defaultSize={35} minSize={25} maxSize={50} className="border-l bg-muted/30 flex flex-col">
              <ScrollArea className="h-full">
                <div className="p-4 space-y-4">
                  {/* Supplier Selection */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      <span className="font-medium">Informasi Supplier</span>
                    </div>

                    <FormField
                      control={form.control}
                      name="supplierId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm">Supplier *</FormLabel>
                          <FormControl>
                            <SupplierSelector
                              value={field.value}
                              onValueChange={field.onChange}
                              placeholder="Pilih supplier..."
                              disabled={isSubmitting}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-2">
                      <FormField
                        control={form.control}
                        name="orderDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm">Tanggal Order *</FormLabel>
                            <FormControl>
                              <Input
                                type="date"
                                {...field}
                                disabled={isSubmitting}
                                className="h-8"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="expectedDelivery"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm">Est. Pengiriman</FormLabel>
                            <FormControl>
                              <Input
                                type="date"
                                {...field}
                                value={field.value || ''}
                                disabled={isSubmitting}
                                className="h-8"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <Separator />

                  {/* Payment Terms */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      <span className="font-medium">Pembayaran</span>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <FormField
                        control={form.control}
                        name="paymentTerms"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm">Termin (hari)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                max="365"
                                {...field}
                                value={field.value || ''}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                                disabled={isSubmitting}
                                placeholder="30"
                                className="h-8"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="paymentMethod"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm">Metode</FormLabel>
                            <Select
                              value={field.value || 'NONE'}
                              onValueChange={(value) => field.onChange(value === 'NONE' ? undefined : value)}
                              disabled={isSubmitting}
                            >
                              <FormControl>
                                <SelectTrigger className="h-8">
                                  <SelectValue placeholder="Pilih metode" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="NONE">Pilih metode</SelectItem>
                                {PAYMENT_METHOD_OPTIONS.map((option) => (
                                  <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <Separator />

                  {/* Order-Level Discount */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Calculator className="h-4 w-4" />
                      <span className="font-medium">Diskon Order</span>
                    </div>

                    <FormField
                      control={form.control}
                      name="discountType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm">Jenis Diskon</FormLabel>
                          <Select
                            value={field.value || 'NONE'}
                            onValueChange={(value) => {
                              field.onChange(value === 'NONE' ? undefined : value);
                              if (value === 'NONE') {
                                form.setValue('discountValue', undefined);
                              }
                            }}
                            disabled={isSubmitting}
                          >
                            <FormControl>
                              <SelectTrigger className="h-8">
                                <SelectValue placeholder="Pilih jenis diskon" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="NONE">Tanpa Diskon</SelectItem>
                              {DISCOUNT_TYPE_OPTIONS.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {watchedDiscountType && (
                      <FormField
                        control={form.control}
                        name="discountValue"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm">
                              {watchedDiscountType === 'PERCENTAGE' ? 'Persentase (%)' : 'Nominal (Rp)'}
                            </FormLabel>
                            <FormControl>
                              {watchedDiscountType === 'FIXED_AMOUNT' ? (
                                <LiveCurrencyInput
                                  value={field.value || undefined}
                                  onValueChange={field.onChange}
                                  disabled={isSubmitting}
                                  placeholder="Rp 0"
                                  className="h-8"
                                />
                              ) : watchedDiscountType === 'PERCENTAGE' ? (
                                <Input
                                  type="number"
                                  min="0"
                                  max="100"
                                  step="0.1"
                                  value={field.value || ''}
                                  onChange={handleOrderPercentageChange}
                                  disabled={isSubmitting}
                                  placeholder="0"
                                  className="h-8"
                                  inputMode="decimal"
                                  title="Masukkan persentase diskon order (0-100)"
                                />
                              ) : null}
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>

                  <Separator />

                  {/* Order Summary */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Calculator className="h-4 w-4" />
                      <span className="font-medium">Ringkasan Order</span>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span className="font-medium">{formatCurrency(formState.subtotal)}</span>
                      </div>

                      {formState.discountAmount > 0 && (
                        <div className="flex justify-between text-green-600">
                          <span>Diskon Order:</span>
                          <span>-{formatCurrency(formState.discountAmount)}</span>
                        </div>
                      )}

                      <div className="flex justify-between">
                        <span>
                          {isPPNLoading ? 'PPN:' :
                            ppnConfig?.isActive ? `PPN (${ppnConfig.taxRate}%):` : 'PPN (Nonaktif):'}
                        </span>
                        <span>{formatCurrency(formState.taxAmount)}</span>
                      </div>

                      <Separator />

                      <div className="flex justify-between text-lg font-semibold">
                        <span>Total:</span>
                        <span>{formatCurrency(formState.totalAmount)}</span>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Form Actions */}
                  <div className="space-y-2">
                    <Button
                      type="submit"
                      className="w-full"
                      disabled={isSubmitting || fields.length === 0}

                    >
                      <FileText className="mr-2 h-4 w-4" />
                      {isSubmitting ? 'Menyimpan...' : submitLabel}
                    </Button>

                    <Button
                      type="button"
                      variant="outline"
                      className="w-full"
                      onClick={onCancel}
                      disabled={isSubmitting}
                    >
                      Batal
                    </Button>
                  </div>
                </div>
              </ScrollArea>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>

        {/* Item Dialog */}
        <PurchaseOrderItemDialog
          isOpen={isItemDialogOpen}
          onClose={() => setIsItemDialogOpen(false)}
          form={form}
          editingIndex={editingItemIndex}
          onSave={(itemData) => {
            if (editingItemIndex !== null) {
              // Update existing item
              Object.keys(itemData).forEach(key => {
                form.setValue(`items.${editingItemIndex}.${key}` as any, itemData[key as keyof typeof itemData]);
              });
            } else {
              // Add new item
              append(itemData);
            }
            setIsItemDialogOpen(false);
            setEditingItemIndex(null);
          }}
          disabled={isSubmitting}
        />
      </form>
    </Form>
  );
}

// Display-only table row component for purchase order items
interface PurchaseOrderItemDisplayRowProps {
  index: number;
  form: any;
  onEdit: () => void;
  onRemove: () => void;
  canRemove: boolean;
  disabled: boolean;
}

function PurchaseOrderItemDisplayRow({
  index,
  form,
  onEdit,
  onRemove,
  canRemove,
  disabled,
}: PurchaseOrderItemDisplayRowProps) {
  const item = form.watch(`items.${index}`);
  const { data: product } = useProduct(item?.productId);

  // Calculate item total with discount
  const itemSubtotal = (item?.quantityOrdered || 0) * (item?.unitPrice || 0);
  const itemDiscountAmount = item?.discountType && item?.discountValue ?
    (item.discountType === 'PERCENTAGE' ?
      itemSubtotal * (item.discountValue / 100) :
      Math.min(item.discountValue, itemSubtotal)
    ) : 0;
  const itemTotal = itemSubtotal - itemDiscountAmount;

  // Find the selected unit name
  const selectedUnit = product?.unitHierarchies?.find(unit => unit.id === item?.unitId);

  // Format discount display
  const formatDiscount = () => {
    if (!item?.discountType || !item?.discountValue) return '-';
    if (item.discountType === 'PERCENTAGE') {
      return `${item.discountValue}%`;
    }
    return formatCurrency(item.discountValue);
  };

  return (
    <TableRow>
      {/* Product Column */}
      <TableCell>
        <div className="space-y-1">
          <div className="font-medium text-sm">
            {product?.name || 'Produk tidak ditemukan'}
          </div>
          {product && (
            <div className="text-xs text-muted-foreground">
              {product.code} • {product.category}
            </div>
          )}
        </div>
      </TableCell>

      {/* Quantity Column */}
      <TableCell>
        <div className="text-sm">
          {item?.quantityOrdered || 0} {selectedUnit?.unit?.name || 'unit'}
        </div>
      </TableCell>

      {/* Price Column */}
      <TableCell className="text-right">
        <div className="text-sm font-medium">
          {formatCurrency(item?.unitPrice || 0)}
        </div>
      </TableCell>

      {/* Discount Column */}
      <TableCell>
        <div className="text-sm">
          {formatDiscount()}
        </div>
      </TableCell>

      {/* Total Column */}
      <TableCell className="text-right">
        <div className="space-y-1">
          <div className="font-medium">{formatCurrency(itemTotal)}</div>
          {itemDiscountAmount > 0 && (
            <div className="text-xs text-green-600">
              -{formatCurrency(itemDiscountAmount)}
            </div>
          )}
        </div>
      </TableCell>

      {/* Actions Column */}
      <TableCell>
        <div className="flex items-center gap-1">
          <Button
            type="button"
            size="icon"
            variant="ghost"
            className="h-7 w-7"
            onClick={onEdit}
            disabled={disabled}
            title="Edit item"
          >
            <Edit3 className="h-3 w-3" />
          </Button>
          {canRemove && (
            <Button
              type="button"
              size="icon"
              variant="ghost"
              className="h-7 w-7"
              onClick={onRemove}
              disabled={disabled}
              title="Hapus item"
            >
              <Trash2 className="h-3 w-3 text-destructive" />
            </Button>
          )}
        </div>
      </TableCell>
    </TableRow>
  );
}