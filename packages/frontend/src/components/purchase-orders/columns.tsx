'use client';

import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal, Eye, Edit, Trash2, FileText, Send, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { PurchaseOrder, PurchaseOrderStatus } from '@/types/purchase-order';
import { getPurchaseOrderStatusLabel, getPurchaseOrderStatusColor } from '@/lib/constants/purchase-order';
import { formatCurrency, formatDate } from '@/lib/utils';
import Link from 'next/link';

interface PurchaseOrderActionsProps {
  purchaseOrder: PurchaseOrder;
  onView: (purchaseOrder: PurchaseOrder) => void;
  onEdit: (purchaseOrder: PurchaseOrder) => void;
  onDelete: (purchaseOrder: PurchaseOrder) => void;
  onCancel: (purchaseOrder: PurchaseOrder) => void;
  onSend: (purchaseOrder: PurchaseOrder) => void;
  onPrint: (purchaseOrder: PurchaseOrder) => void;
}

function PurchaseOrderActions({
  purchaseOrder,
  onView,
  onEdit,
  onDelete,
  onCancel,
  onSend,
  onPrint,
}: PurchaseOrderActionsProps) {
  const canEdit = purchaseOrder.status === PurchaseOrderStatus.DRAFT;
  const canSend = purchaseOrder.status === PurchaseOrderStatus.SUBMITTED;
  const canCancel = [
    PurchaseOrderStatus.DRAFT,
    PurchaseOrderStatus.SUBMITTED,
    PurchaseOrderStatus.SUBMITTED,
    PurchaseOrderStatus.ORDERED,
  ].includes(purchaseOrder.status);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Buka menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Aksi</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={() => onView(purchaseOrder)}>
          <Eye className="mr-2 h-4 w-4" />
          Lihat Cepat
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href={`/dashboard/purchase-orders/${purchaseOrder.id}`}>
            <FileText className="mr-2 h-4 w-4" />
            Lihat Detail
          </Link>
        </DropdownMenuItem>

        {canEdit && (
          <DropdownMenuItem onClick={() => onEdit(purchaseOrder)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </DropdownMenuItem>
        )}

        <DropdownMenuItem onClick={() => onPrint(purchaseOrder)}>
          <FileText className="mr-2 h-4 w-4" />
          Cetak PDF
        </DropdownMenuItem>

        <DropdownMenuSeparator />



        {canSend && (
          <DropdownMenuItem onClick={() => onSend(purchaseOrder)}>
            <Send className="mr-2 h-4 w-4" />
            Kirim ke Supplier
          </DropdownMenuItem>
        )}

        {canCancel && (
          <DropdownMenuItem onClick={() => onCancel(purchaseOrder)}>
            <XCircle className="mr-2 h-4 w-4" />
            Batalkan
          </DropdownMenuItem>
        )}

        {purchaseOrder.status === PurchaseOrderStatus.DRAFT && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onDelete(purchaseOrder)}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Hapus
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function createPurchaseOrderColumns({
  onView,
  onEdit,
  onDelete,
  onCancel,
  onSend,
  onPrint,
}: {
  onView: (purchaseOrder: PurchaseOrder) => void;
  onEdit: (purchaseOrder: PurchaseOrder) => void;
  onDelete: (purchaseOrder: PurchaseOrder) => void;
  onCancel: (purchaseOrder: PurchaseOrder) => void;
  onSend: (purchaseOrder: PurchaseOrder) => void;
  onPrint: (purchaseOrder: PurchaseOrder) => void;
}): ColumnDef<PurchaseOrder>[] {
  return [
    {
      accessorKey: 'orderNumber',
      header: 'Nomor PO',
      cell: ({ row }) => {
        const purchaseOrder = row.original;
        return (
          <div className="font-medium">
            {purchaseOrder.orderNumber}
          </div>
        );
      },
    },
    {
      accessorKey: 'supplier',
      header: 'Supplier',
      cell: ({ row }) => {
        const supplier = row.original.supplier;
        return (
          <div className="min-w-0">
            <div className="font-medium truncate">{supplier.name}</div>
            <div className="text-sm text-muted-foreground truncate">
              {supplier.code} • {supplier.type}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'orderDate',
      header: 'Tanggal Order',
      cell: ({ row }) => {
        return (
          <div className="text-sm">
            {formatDate(row.original.orderDate)}
          </div>
        );
      },
    },
    {
      accessorKey: 'expectedDelivery',
      header: 'Estimasi Pengiriman',
      cell: ({ row }) => {
        const expectedDelivery = row.original.expectedDelivery;
        if (!expectedDelivery) {
          return <span className="text-muted-foreground">-</span>;
        }
        return (
          <div className="text-sm">
            {formatDate(expectedDelivery)}
          </div>
        );
      },
    },
    {
      accessorKey: 'totalAmount',
      header: 'Total',
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {formatCurrency(row.original.totalAmount)}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.original.status;
        const statusLabel = getPurchaseOrderStatusLabel(status);
        const statusColor = getPurchaseOrderStatusColor(status);

        return (
          <Badge variant="outline" className={statusColor}>
            {statusLabel}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'items',
      header: 'Item',
      cell: ({ row }) => {
        const items = row.original.items;
        const itemCount = items.length;
        const totalQuantity = items.reduce((sum, item) => sum + item.quantityOrdered, 0);

        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="text-sm cursor-help">
                  {itemCount} item{itemCount > 1 ? 's' : ''}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-sm">
                  <div>{itemCount} jenis produk</div>
                  <div>{totalQuantity} total kuantitas</div>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Dibuat',
      cell: ({ row }) => {
        const createdBy = row.original.createdByUser;
        return (
          <div className="text-sm">
            <div>{formatDate(row.original.createdAt)}</div>
            {createdBy && (
              <div className="text-muted-foreground truncate">
                oleh {createdBy.name}
              </div>
            )}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Aksi',
      cell: ({ row }) => {
        const purchaseOrder = row.original;

        return (
          <PurchaseOrderActions
            purchaseOrder={purchaseOrder}
            onView={onView}
            onEdit={onEdit}
            onDelete={onDelete}
            onCancel={onCancel}
            onSend={onSend}
            onPrint={onPrint}
          />
        );
      },
    },
  ];
}
