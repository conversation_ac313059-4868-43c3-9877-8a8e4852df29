'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  ShoppingCart,
  Building2,
  Calendar,
  DollarSign,
  Package,
  Phone,
  Mail,
  MapPin,
  Edit,
  ExternalLink,
  Send,
  XCircle,
  FileText,
  User,
  CreditCard
} from 'lucide-react';
import { PurchaseOrder, PurchaseOrderStatus } from '@/types/purchase-order';
import { formatCurrency, formatDate } from '@/lib/utils';
import { 
  getPurchaseOrderStatusLabel, 
  getPurchaseOrderStatusColor,
  getPaymentMethodLabel 
} from '@/lib/constants/purchase-order';

interface PurchaseOrderQuickViewProps {
  purchaseOrder: PurchaseOrder | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (purchaseOrder: PurchaseOrder) => void;
  onCancel?: (purchaseOrder: PurchaseOrder, reason: string) => void;
  onSend?: (purchaseOrder: PurchaseOrder) => void;
  onPrint?: (purchaseOrder: PurchaseOrder) => void;
  onViewDetails?: (purchaseOrder: PurchaseOrder) => void;
}

export function PurchaseOrderQuickView({
  purchaseOrder,
  open,
  onOpenChange,
  onEdit,
  onCancel,
  onSend,
  onPrint,
  onViewDetails,
}: PurchaseOrderQuickViewProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [cancelReason, setCancelReason] = useState('');

  if (!purchaseOrder) return null;

  const canEdit = purchaseOrder.status === PurchaseOrderStatus.DRAFT;
  const canSend = purchaseOrder.status === PurchaseOrderStatus.SUBMITTED;
  const canCancel = [
    PurchaseOrderStatus.DRAFT,
    PurchaseOrderStatus.SUBMITTED,
    PurchaseOrderStatus.ORDERED,
  ].includes(purchaseOrder.status);

  const handleAction = async (action: () => void | Promise<void>) => {
    setIsLoading(true);
    try {
      await action();
      onOpenChange(false);
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      if (process.env.NODE_ENV === 'development') {
        console.error('Action failed:', error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (cancelReason.trim() && onCancel) {
      handleAction(() => onCancel(purchaseOrder, cancelReason));
      setCancelReason('');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Detail Purchase Order {purchaseOrder.orderNumber}
          </DialogTitle>
          <DialogDescription>
            Informasi lengkap purchase order dan status pemesanan
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Status and Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={getPurchaseOrderStatusColor(purchaseOrder.status)}>
                {getPurchaseOrderStatusLabel(purchaseOrder.status)}
              </Badge>
              {purchaseOrder.paymentMethod && (
                <Badge variant="outline">
                  {getPaymentMethodLabel(purchaseOrder.paymentMethod)}
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              {onEdit && canEdit && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleAction(() => onEdit(purchaseOrder))}
                  disabled={isLoading}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}

              {onSend && canSend && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => handleAction(() => onSend(purchaseOrder))}
                  disabled={isLoading}
                >
                  <Send className="h-4 w-4 mr-2" />
                  Kirim ke Supplier
                </Button>
              )}

              {onPrint && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleAction(() => onPrint(purchaseOrder))}
                  disabled={isLoading}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Cetak PDF
                </Button>
              )}

              {onViewDetails && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewDetails(purchaseOrder)}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Detail Lengkap
                </Button>
              )}
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {/* Purchase Order Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Informasi Purchase Order
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Nomor PO</p>
                    <p className="font-medium">{purchaseOrder.orderNumber}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Tanggal Order</p>
                    <p className="font-medium">{formatDate(purchaseOrder.orderDate)}</p>
                  </div>
                  {purchaseOrder.expectedDelivery && (
                    <div>
                      <p className="text-muted-foreground">Estimasi Pengiriman</p>
                      <p className="font-medium">{formatDate(purchaseOrder.expectedDelivery)}</p>
                    </div>
                  )}
                  <div>
                    <p className="text-muted-foreground">Status</p>
                    <Badge variant="outline" className={getPurchaseOrderStatusColor(purchaseOrder.status)}>
                      {getPurchaseOrderStatusLabel(purchaseOrder.status)}
                    </Badge>
                  </div>
                  {purchaseOrder.createdByUser && (
                    <div>
                      <p className="text-muted-foreground">Dibuat oleh</p>
                      <p className="font-medium">{purchaseOrder.createdByUser.name}</p>
                    </div>
                  )}
                  <div>
                    <p className="text-muted-foreground">Dibuat pada</p>
                    <p className="font-medium">{formatDate(purchaseOrder.createdAt)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Supplier Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Informasi Supplier
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <p className="font-medium text-lg">{purchaseOrder.supplier.name}</p>
                    <p className="text-sm text-muted-foreground">{purchaseOrder.supplier.code}</p>
                  </div>
                  
                  {purchaseOrder.supplier.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{purchaseOrder.supplier.phone}</span>
                    </div>
                  )}

                  {purchaseOrder.supplier.email && (
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{purchaseOrder.supplier.email}</span>
                    </div>
                  )}

                  {purchaseOrder.supplier.city && (
                    <div className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                      <span className="text-sm">{purchaseOrder.supplier.city}</span>
                    </div>
                  )}
                  
                  <Badge variant="secondary" className="text-xs">
                    {purchaseOrder.supplier.type}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Financial Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Ringkasan Keuangan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Subtotal</p>
                  <p className="font-medium">{formatCurrency(purchaseOrder.subtotal)}</p>
                </div>
                {purchaseOrder.discountAmount > 0 && (
                  <div>
                    <p className="text-muted-foreground">Diskon</p>
                    <p className="font-medium text-green-600">-{formatCurrency(purchaseOrder.discountAmount)}</p>
                  </div>
                )}
                {purchaseOrder.taxAmount > 0 && (
                  <div>
                    <p className="text-muted-foreground">Pajak</p>
                    <p className="font-medium">{formatCurrency(purchaseOrder.taxAmount)}</p>
                  </div>
                )}
                <div>
                  <p className="text-muted-foreground">Total</p>
                  <p className="font-bold text-lg">{formatCurrency(purchaseOrder.totalAmount)}</p>
                </div>
              </div>

              {(purchaseOrder.paymentMethod || purchaseOrder.paymentTerms) && (
                <>
                  <Separator className="my-4" />
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {purchaseOrder.paymentMethod && (
                      <div>
                        <p className="text-muted-foreground">Metode Pembayaran</p>
                        <div className="flex items-center gap-2">
                          <CreditCard className="h-4 w-4" />
                          <span className="font-medium">{getPaymentMethodLabel(purchaseOrder.paymentMethod)}</span>
                        </div>
                      </div>
                    )}
                    {purchaseOrder.paymentTerms && (
                      <div>
                        <p className="text-muted-foreground">Termin Pembayaran</p>
                        <p className="font-medium">{purchaseOrder.paymentTerms} hari</p>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Purchase Order Items */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Package className="h-5 w-5" />
                Item Purchase Order ({purchaseOrder.items.length} item)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {purchaseOrder.items.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{item.product.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {item.product.code} • {item.product.manufacturer}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {item.quantityOrdered} {item.unit.abbreviation} × {formatCurrency(item.unitPrice)}
                      </div>
                      {item.notes && (
                        <div className="text-xs text-muted-foreground mt-1">
                          Catatan: {item.notes}
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(item.totalPrice)}</div>
                      {item.discountAmount > 0 && (
                        <div className="text-sm text-green-600">
                          Diskon: -{formatCurrency(item.discountAmount)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Notes and Delivery Information */}
          {(purchaseOrder.deliveryNotes || purchaseOrder.deliveryAddress) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informasi Pengiriman</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {purchaseOrder.deliveryAddress && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Alamat Pengiriman</p>
                    <p className="text-sm">{purchaseOrder.deliveryAddress}</p>
                  </div>
                )}
                {purchaseOrder.deliveryContact && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Kontak Pengiriman</p>
                    <p className="text-sm">{purchaseOrder.deliveryContact}</p>
                  </div>
                )}
                {purchaseOrder.deliveryPhone && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Telepon Pengiriman</p>
                    <p className="text-sm">{purchaseOrder.deliveryPhone}</p>
                  </div>
                )}
                {purchaseOrder.deliveryNotes && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Catatan Pengiriman</p>
                    <p className="text-sm">{purchaseOrder.deliveryNotes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end gap-2 pt-4 border-t">
            {onCancel && canCancel && (
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  placeholder="Alasan pembatalan..."
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  className="px-3 py-2 border rounded-md text-sm"
                />
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isLoading || !cancelReason.trim()}
                  className="text-red-600 hover:text-red-700"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Batalkan
                </Button>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
