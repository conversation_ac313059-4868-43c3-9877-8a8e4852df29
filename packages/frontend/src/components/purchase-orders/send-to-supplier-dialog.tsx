'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  CheckCircle,
  Download,
  Send,
  FileText,
  Package,
  ArrowRight,
  Clock,
  AlertTriangle,
  ExternalLink,
} from 'lucide-react';
import { PurchaseOrder } from '@/types/purchase-order';
import { formatCurrency, formatDate } from '@/lib/utils';
import { useRouter } from 'next/navigation';

interface SendToSupplierDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  purchaseOrder: PurchaseOrder | null;
  onDownloadPdf: (purchaseOrder: PurchaseOrder) => Promise<void>;
  onSendToSupplier: (purchaseOrder: PurchaseOrder) => Promise<void>;
  isDownloading?: boolean;
  isSending?: boolean;
}

type WorkflowStep = 'download' | 'sign' | 'send' | 'receipt' | 'complete';

const WORKFLOW_STEPS = [
  {
    id: 'download' as const,
    title: 'Unduh Purchase Order',
    description: 'Unduh dokumen purchase order dalam format PDF',
    icon: Download,
    action: true,
  },
  {
    id: 'sign' as const,
    title: 'Tanda Tangan Dokumen',
    description: 'Cetak dan tanda tangani dokumen purchase order',
    icon: FileText,
    action: false,
  },
  {
    id: 'send' as const,
    title: 'Kirim ke Supplier',
    description: 'Kirim dokumen yang sudah ditandatangani ke supplier',
    icon: Send,
    action: true,
  },
  {
    id: 'receipt' as const,
    title: 'Penerimaan Barang',
    description: 'Setelah barang tiba, lakukan inspeksi di halaman Penerimaan Barang',
    icon: Package,
    action: true,
  },
];

export function SendToSupplierDialog({
  open,
  onOpenChange,
  purchaseOrder,
  onDownloadPdf,
  onSendToSupplier,
  isDownloading = false,
  isSending = false,
}: SendToSupplierDialogProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<WorkflowStep>('download');
  const [completedSteps, setCompletedSteps] = useState<Set<WorkflowStep>>(new Set());

  if (!purchaseOrder) return null;

  const handleDownloadPdf = async () => {
    try {
      await onDownloadPdf(purchaseOrder);
      setCompletedSteps(prev => new Set([...prev, 'download']));
      setCurrentStep('sign');
      toast.success('PDF berhasil diunduh');
    } catch (error) {
      console.error('Failed to download PDF:', error);
    }
  };

  const handleSendToSupplier = async () => {
    try {
      await onSendToSupplier(purchaseOrder);
      setCompletedSteps(prev => new Set([...prev, 'send']));
      setCurrentStep('receipt');
      toast.success('Purchase order berhasil dikirim ke supplier');
    } catch (error) {
      console.error('Failed to send to supplier:', error);
    }
  };

  const handleGoToReceipt = () => {
    router.push('/dashboard/goods-receipts');
    onOpenChange(false);
  };

  const handleStepAction = (stepId: WorkflowStep) => {
    switch (stepId) {
      case 'download':
        handleDownloadPdf();
        break;
      case 'send':
        handleSendToSupplier();
        break;
      case 'receipt':
        handleGoToReceipt();
        break;
      case 'sign':
        setCompletedSteps(prev => new Set([...prev, 'sign']));
        setCurrentStep('send');
        break;
    }
  };

  const isStepCompleted = (stepId: WorkflowStep) => completedSteps.has(stepId);
  const isStepCurrent = (stepId: WorkflowStep) => currentStep === stepId;
  const isStepDisabled = (stepId: WorkflowStep) => {
    const stepIndex = WORKFLOW_STEPS.findIndex(s => s.id === stepId);
    const currentIndex = WORKFLOW_STEPS.findIndex(s => s.id === currentStep);
    return stepIndex > currentIndex && !isStepCompleted(stepId);
  };

  const getStepStatus = (stepId: WorkflowStep) => {
    if (isStepCompleted(stepId)) return 'completed';
    if (isStepCurrent(stepId)) return 'current';
    if (isStepDisabled(stepId)) return 'disabled';
    return 'pending';
  };

  const getActionButtonText = (stepId: WorkflowStep) => {
    switch (stepId) {
      case 'download':
        return isDownloading ? 'Mengunduh...' : 'Unduh PDF';
      case 'sign':
        return 'Sudah Ditandatangani';
      case 'send':
        return isSending ? 'Mengirim...' : 'Kirim ke Supplier';
      case 'receipt':
        return 'Buka Penerimaan Barang';
      default:
        return 'Lanjutkan';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Kirim ke Supplier
          </DialogTitle>
          <DialogDescription>
            Ikuti langkah-langkah berikut untuk mengirim purchase order ke supplier
          </DialogDescription>
        </DialogHeader>

        {/* Purchase Order Summary */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Ringkasan Purchase Order</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Nomor PO:</span>
              <span className="font-medium">{purchaseOrder.orderNumber}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Supplier:</span>
              <span className="font-medium">{purchaseOrder.supplier.name}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Tanggal Order:</span>
              <span>{formatDate(purchaseOrder.orderDate)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Total:</span>
              <span className="font-bold">{formatCurrency(purchaseOrder.totalAmount)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Jumlah Item:</span>
              <span>{purchaseOrder.items.length} item</span>
            </div>
          </CardContent>
        </Card>

        <Separator />

        {/* Workflow Steps */}
        <div className="space-y-4">
          <h3 className="font-medium">Langkah-langkah Pengiriman</h3>
          
          {WORKFLOW_STEPS.map((step, index) => {
            const status = getStepStatus(step.id);
            const Icon = step.icon;
            
            return (
              <Card key={step.id} className={`transition-all ${
                status === 'current' ? 'ring-2 ring-primary' : ''
              } ${status === 'disabled' ? 'opacity-50' : ''}`}>
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    {/* Step Number/Icon */}
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      status === 'completed' 
                        ? 'bg-green-100 text-green-600' 
                        : status === 'current'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted text-muted-foreground'
                    }`}>
                      {status === 'completed' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Icon className="h-4 w-4" />
                      )}
                    </div>

                    {/* Step Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm">{step.title}</h4>
                        {status === 'completed' && (
                          <Badge variant="secondary" className="text-xs">
                            Selesai
                          </Badge>
                        )}
                        {status === 'current' && (
                          <Badge variant="default" className="text-xs">
                            Aktif
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        {step.description}
                      </p>

                      {/* Step-specific content */}
                      {step.id === 'sign' && status === 'current' && (
                        <div className="bg-amber-50 border border-amber-200 rounded-md p-3 mb-3">
                          <div className="flex items-start gap-2">
                            <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5" />
                            <div className="text-sm">
                              <p className="font-medium text-amber-800">Petunjuk Tanda Tangan:</p>
                              <ul className="text-amber-700 mt-1 space-y-1">
                                <li>• Cetak dokumen PDF yang sudah diunduh</li>
                                <li>• Tanda tangani di bagian yang telah disediakan</li>
                                <li>• Pastikan cap perusahaan juga dicantumkan</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      )}

                      {step.id === 'receipt' && status === 'current' && (
                        <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-3">
                          <div className="flex items-start gap-2">
                            <Clock className="h-4 w-4 text-blue-600 mt-0.5" />
                            <div className="text-sm">
                              <p className="font-medium text-blue-800">Menunggu Barang Tiba:</p>
                              <p className="text-blue-700 mt-1">
                                Setelah supplier mengkonfirmasi dan barang tiba, lakukan inspeksi 
                                dan tandai penerimaan barang sebagai berhasil di halaman Penerimaan Barang.
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Action Button */}
                      {step.action && (status === 'current' || (step.id === 'receipt' && status === 'current')) && (
                        <Button
                          onClick={() => handleStepAction(step.id)}
                          disabled={
                            (step.id === 'download' && isDownloading) ||
                            (step.id === 'send' && isSending)
                          }
                          className="w-full sm:w-auto"
                          variant={step.id === 'receipt' ? 'outline' : 'default'}
                        >
                          {step.id === 'receipt' && <ExternalLink className="h-4 w-4 mr-2" />}
                          {getActionButtonText(step.id)}
                        </Button>
                      )}
                    </div>

                    {/* Arrow to next step */}
                    {index < WORKFLOW_STEPS.length - 1 && (
                      <div className="flex-shrink-0 text-muted-foreground">
                        <ArrowRight className="h-4 w-4" />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Tutup
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
