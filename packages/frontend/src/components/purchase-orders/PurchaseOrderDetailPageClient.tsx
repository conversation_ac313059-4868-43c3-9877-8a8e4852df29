'use client';

import { useRouter } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { PurchaseOrderDetail } from '@/components/purchase-orders/purchase-order-detail';
import {
  usePurchaseOrder,
  useCancelPurchaseOrder,
  useSendPurchaseOrder,
} from '@/hooks/usePurchaseOrders';
import { navigateBackWithFallback } from '@/lib/utils/navigation';
import { toast } from 'sonner';

interface PurchaseOrderDetailPageClientProps {
  purchaseOrderId: string;
}

export function PurchaseOrderDetailPageClient({ purchaseOrderId }: PurchaseOrderDetailPageClientProps) {
  const router = useRouter();
  const { data: purchaseOrder, isLoading, error, refetch } = usePurchaseOrder(purchaseOrderId);
  const cancelPurchaseOrderMutation = useCancelPurchaseOrder();
  const sendPurchaseOrderMutation = useSendPurchaseOrder();

  const handleEdit = () => {
    router.push(`/dashboard/purchase-orders/${purchaseOrderId}/edit`);
  };



  const handleCancel = async (reason: string) => {
    try {
      await cancelPurchaseOrderMutation.mutateAsync({
        id: purchaseOrderId,
        reason,
      });
      // Refresh the purchase order data
      refetch();
    } catch (error) {
      console.error('Failed to cancel purchase order:', error);
    }
  };

  const handleSend = async () => {
    try {
      await sendPurchaseOrderMutation.mutateAsync(purchaseOrderId);
      // Refresh the purchase order data
      refetch();
    } catch (error) {
      console.error('Failed to send purchase order:', error);
    }
  };

  const handlePrint = async () => {
    try {
      // This would typically generate and download a PDF
      toast.info('Fitur cetak akan segera tersedia');
    } catch (error) {
      console.error('Failed to print purchase order:', error);
      toast.error('Gagal mencetak purchase order');
    }
  };

  const handleBack = () => {
    navigateBackWithFallback(router, '/dashboard/purchase-orders');
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton - Simplified for client loading */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Skeleton className="h-9 w-20" />
            <div className="space-y-2">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-20" />
          </div>
        </div>

        {/* Content Skeleton - Focused on data-dependent parts */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            {/* Info Card Skeleton */}
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <div key={i} className="space-y-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-5 w-32" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Items Table Skeleton */}
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            {/* Sidebar Cards Skeleton */}
            {Array.from({ length: 3 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-32" />
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    {Array.from({ length: 4 }).map((_, j) => (
                      <div key={j} className="space-y-1">
                        <Skeleton className="h-3 w-20" />
                        <Skeleton className="h-4 w-28" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error || !purchaseOrder) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <button
            onClick={handleBack}
            className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground"
          >
            ← Kembali
          </button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Purchase Order Detail</h1>
            <p className="text-muted-foreground">
              Purchase order tidak ditemukan atau terjadi kesalahan
            </p>
          </div>
        </div>

        {/* Error Message */}
        <div className="rounded-lg border border-destructive/50 bg-destructive/10 p-4">
          <p className="text-sm text-destructive">
            {error ? 'Gagal memuat data purchase order' : 'Purchase order tidak ditemukan'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <PurchaseOrderDetail
      purchaseOrder={purchaseOrder}
      onEdit={handleEdit}
      onCancel={handleCancel}
      onSend={handleSend}
      onPrint={handlePrint}
      onBack={handleBack}
      isLoading={
        cancelPurchaseOrderMutation.isPending ||
        sendPurchaseOrderMutation.isPending
      }
    />
  );
}
