"use client";

import { useProduct } from "@/hooks/useProducts";
import { Product } from "@/types/product";
import { useEffect, useState } from "react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "../ui/dialog";
import { Label } from "../ui/label";
import { ProductSelector } from "../ui/product-selector";
import { MultiUnitQuantityInput } from "./multi-unit-quantity-input";
import { formatCurrency } from "@/lib/utils";
import { LiveCurrencyInput } from "../ui/currency-input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { DISCOUNT_TYPE_OPTIONS } from "@/lib/constants/purchase-order";
import { Input } from "../ui/input";
import { Button } from "../ui/button";

// Item Dialog Component
interface PurchaseOrderItemDialogProps {
  isOpen: boolean;
  onClose: () => void;
  form: any;
  editingIndex: number | null;
  onSave: (itemData: any) => void;
  disabled: boolean;
}

export function PurchaseOrderItemDialog({
  isOpen,
  onClose,
  form,
  editingIndex,
  onSave,
  disabled,
}: PurchaseOrderItemDialogProps) {
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [itemForm, setItemForm] = useState({
    productId: '',
    unitId: '',
    quantityOrdered: 1,
    unitPrice: 0,
    discountType: undefined as string | undefined,
    discountValue: undefined as number | undefined,
    expectedDelivery: '',
    qualitySpecs: '',
    notes: '',
    updateSellingPrice: false,
    newSellingPrice: undefined as number | undefined,
  });

  const { data: product } = useProduct(itemForm.productId);

  // Initialize form when dialog opens
  useEffect(() => {
    if (isOpen) {
      if (editingIndex !== null) {
        // Editing existing item
        const existingItem = form.getValues(`items.${editingIndex}`);
        setItemForm(existingItem);
      } else {
        // Adding new item
        setItemForm({
          productId: '',
          unitId: '',
          quantityOrdered: 1,
          unitPrice: 0,
          discountType: undefined,
          discountValue: undefined,
          expectedDelivery: '',
          qualitySpecs: '',
          notes: '',
          updateSellingPrice: false,
          newSellingPrice: undefined,
        });
      }
    }
  }, [isOpen, editingIndex, form]);

  useEffect(() => {
    if (product) {
      setSelectedProduct(product);
    }
  }, [product]);

  const handleProductChange = (newProductId: string) => {
    setItemForm(prev => ({
      ...prev,
      productId: newProductId,
      unitId: '',
      unitPrice: 0,
    }));
  };

  // Handle unit change and auto-populate price
  const handleUnitChange = (unitId: string) => {
    setItemForm(prev => {
      const newForm = { ...prev, unitId };

      // Auto-populate unit price with cost price if available
      if (selectedProduct && unitId) {
        const selectedUnitHierarchy = selectedProduct.unitHierarchies?.find(
          hierarchy => hierarchy.unitId === unitId
        );

        if (selectedUnitHierarchy?.costPrice) {
          newForm.unitPrice = selectedUnitHierarchy.costPrice;
        }
      }

      return newForm;
    });
  };

  // Get selected unit hierarchy for price display
  const selectedUnitHierarchy = selectedProduct?.unitHierarchies?.find(
    hierarchy => hierarchy.unitId === itemForm.unitId
  );

  const handleSave = () => {
    onSave(itemForm);
  };

  const handleDiscountTypeChange = (type: string) => {
    setItemForm(prev => ({
      ...prev,
      discountType: type === 'NONE' ? undefined : type,
      discountValue: type === 'NONE' ? undefined : prev.discountValue,
    }));
  };

  const handlePercentageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    if (inputValue === '') {
      setItemForm(prev => ({ ...prev, discountValue: undefined }));
      return;
    }

    const value = parseFloat(inputValue);
    if (!isNaN(value) && value >= 0 && value <= 100) {
      setItemForm(prev => ({ ...prev, discountValue: value }));
    }
  };

  // Calculate item total
  const itemSubtotal = itemForm.quantityOrdered * itemForm.unitPrice;
  const itemDiscountAmount = itemForm.discountType && itemForm.discountValue ?
    (itemForm.discountType === 'PERCENTAGE' ?
      itemSubtotal * (itemForm.discountValue / 100) :
      Math.min(itemForm.discountValue, itemSubtotal)
    ) : 0;
  const itemTotal = itemSubtotal - itemDiscountAmount;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editingIndex !== null ? 'Edit Item Purchase Order' : 'Tambah Item Purchase Order'}
          </DialogTitle>
          <DialogDescription>
            {editingIndex !== null ?
              'Ubah informasi item yang sudah ada' :
              'Tambahkan item baru ke purchase order'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Product Selector */}
          <div className="space-y-2">
            <Label>Produk *</Label>
            <ProductSelector
              value={itemForm.productId}
              onValueChange={handleProductChange}
              placeholder="Pilih produk..."
              disabled={disabled}
            />
          </div>

          {/* Quantity and Unit */}
          {selectedProduct && (
            <div className="space-y-2">
              <Label>Kuantitas dan Satuan *</Label>
              <MultiUnitQuantityInput
                productId={selectedProduct.id}
                units={selectedProduct.unitHierarchies || []}
                selectedUnitId={itemForm.unitId}
                quantity={itemForm.quantityOrdered}
                onUnitChange={handleUnitChange}
                onQuantityChange={(quantity) => setItemForm(prev => ({ ...prev, quantityOrdered: quantity }))}
                disabled={disabled}
              />
            </div>
          )}

          {/* Pricing Information */}
          {selectedUnitHierarchy && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                {/* Cost Price (Auto-populated) */}
                <div className="space-y-2">
                  <Label>Harga Beli (Otomatis)</Label>
                  <div className="p-2 bg-muted rounded-md text-sm">
                    {selectedUnitHierarchy.costPrice ?
                      formatCurrency(selectedUnitHierarchy.costPrice) :
                      'Tidak tersedia'
                    }
                  </div>
                </div>

                {/* Current Selling Price (Reference) */}
                <div className="space-y-2">
                  <Label>Harga Jual Saat Ini</Label>
                  <div className="p-2 bg-muted rounded-md text-sm">
                    {selectedUnitHierarchy.sellingPrice ?
                      formatCurrency(selectedUnitHierarchy.sellingPrice) :
                      'Tidak tersedia'
                    }
                  </div>
                </div>
              </div>

              {/* Optional Selling Price Update */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Harga Jual Baru (Opsional)</Label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="updateSellingPrice"
                      checked={itemForm.updateSellingPrice || false}
                      onChange={(e) => setItemForm(prev => ({
                        ...prev,
                        updateSellingPrice: e.target.checked,
                        newSellingPrice: e.target.checked ? (selectedUnitHierarchy?.sellingPrice || 0) : undefined
                      }))}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="updateSellingPrice" className="text-sm font-normal">
                      Perbarui harga jual
                    </Label>
                  </div>
                </div>

                {itemForm.updateSellingPrice && (
                  <div className="space-y-2">
                    <LiveCurrencyInput
                      value={itemForm.newSellingPrice || 0}
                      onValueChange={(value) => setItemForm(prev => ({ ...prev, newSellingPrice: value || 0 }))}
                      disabled={disabled}
                      placeholder="Rp 0"
                    />
                    {/* Margin Calculation Display */}
                    {itemForm.newSellingPrice && itemForm.unitPrice && (
                      <div className="text-xs text-muted-foreground">
                        Margin: {itemForm.newSellingPrice > itemForm.unitPrice ?
                          `${(((itemForm.newSellingPrice - itemForm.unitPrice) / itemForm.unitPrice) * 100).toFixed(1)}%` :
                          'Harga jual harus lebih tinggi dari harga beli'
                        }
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Unit Price (Editable) */}
          <div className="space-y-2">
            <Label>Harga Satuan Purchase Order *</Label>
            <LiveCurrencyInput
              value={itemForm.unitPrice}
              onValueChange={(value) => setItemForm(prev => ({ ...prev, unitPrice: value || 0 }))}
              disabled={disabled}
              placeholder="Rp 0"
            />
            <p className="text-xs text-muted-foreground">
              Harga akan otomatis terisi dari harga beli produk. Anda dapat mengubahnya sesuai kebutuhan.
            </p>
          </div>

          {/* Discount */}
          <div className="space-y-2">
            <Label>Diskon Item</Label>
            <div className="grid grid-cols-2 gap-2">
              <Select
                value={itemForm.discountType || 'NONE'}
                onValueChange={handleDiscountTypeChange}
                disabled={disabled}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih jenis diskon" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="NONE">Tanpa Diskon</SelectItem>
                  {DISCOUNT_TYPE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {itemForm.discountType && (
                <div>
                  {itemForm.discountType === 'FIXED_AMOUNT' ? (
                    <LiveCurrencyInput
                      value={itemForm.discountValue}
                      onValueChange={(value) => setItemForm(prev => ({ ...prev, discountValue: value }))}
                      disabled={disabled}
                      placeholder="Rp 0"
                    />
                  ) : itemForm.discountType === 'PERCENTAGE' ? (
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      step="0.1"
                      value={itemForm.discountValue || ''}
                      onChange={handlePercentageChange}
                      disabled={disabled}
                      placeholder="0"
                      inputMode="decimal"
                      title="Masukkan persentase diskon (0-100)"
                    />
                  ) : null}
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label>Catatan</Label>
            <Input
              value={itemForm.notes}
              onChange={(e) => setItemForm(prev => ({ ...prev, notes: e.target.value }))}
              disabled={disabled}
              placeholder="Catatan untuk item ini..."
            />
          </div>

          {/* Total Display */}
          <div className="bg-muted p-3 rounded-lg">
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>{formatCurrency(itemSubtotal)}</span>
              </div>
              {itemDiscountAmount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Diskon:</span>
                  <span>-{formatCurrency(itemDiscountAmount)}</span>
                </div>
              )}
              <div className="flex justify-between font-medium border-t pt-1">
                <span>Total:</span>
                <span>{formatCurrency(itemTotal)}</span>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={disabled}
          >
            Batal
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            disabled={disabled || !itemForm.productId || !itemForm.unitId}
          >
            {disabled ? 'Menyimpan...' : 'Simpan'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
