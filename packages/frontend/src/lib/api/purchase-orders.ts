import { apiClient } from '../axios';
import {
  PurchaseOrder,
  PurchaseOrderQueryParams,
  PurchaseOrderListResponse,
  PurchaseOrderStatsResponse,
  CreatePurchaseOrderDto,
  UpdatePurchaseOrderDto,
  PurchaseOrderStatusUpdateDto,
} from '@/types/purchase-order';

export const purchaseOrdersApi = {
  // Get all purchase orders with filtering and pagination
  getPurchaseOrders: async (params: PurchaseOrderQueryParams = {}): Promise<PurchaseOrderListResponse> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    const response = await apiClient.get(`/purchase-orders?${searchParams.toString()}`);
    return response.data;
  },

  // Get a specific purchase order by ID
  getPurchaseOrder: async (id: string): Promise<PurchaseOrder> => {
    const response = await apiClient.get(`/purchase-orders/${id}`);
    return response.data;
  },

  // Create a new purchase order
  createPurchaseOrder: async (data: CreatePurchaseOrderDto): Promise<PurchaseOrder> => {
    const response = await apiClient.post('/purchase-orders', data);
    return response.data;
  },

  // Update an existing purchase order
  updatePurchaseOrder: async (id: string, data: UpdatePurchaseOrderDto): Promise<PurchaseOrder> => {
    const response = await apiClient.patch(`/purchase-orders/${id}`, data);
    return response.data;
  },

  // Delete a purchase order
  deletePurchaseOrder: async (id: string): Promise<void> => {
    await apiClient.delete(`/purchase-orders/${id}`);
  },



  // Cancel a purchase order
  cancelPurchaseOrder: async (id: string, data: { reason?: string } = {}): Promise<PurchaseOrder> => {
    const response = await apiClient.post(`/purchase-orders/${id}/cancel`, data);
    return response.data;
  },

  // Update purchase order status
  updatePurchaseOrderStatus: async (id: string, data: PurchaseOrderStatusUpdateDto): Promise<PurchaseOrder> => {
    const response = await apiClient.patch(`/purchase-orders/${id}/status`, data);
    return response.data;
  },

  // Get purchase order statistics
  getPurchaseOrderStats: async (period?: string): Promise<PurchaseOrderStatsResponse> => {
    const params = period ? `?period=${period}` : '';
    const response = await apiClient.get(`/purchase-orders/stats${params}`);
    return response.data;
  },

  // Export purchase orders
  exportPurchaseOrders: async (params: PurchaseOrderQueryParams & { format?: 'xlsx' | 'csv' } = {}): Promise<Blob> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    const response = await apiClient.get(`/purchase-orders/export?${searchParams.toString()}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Generate purchase order PDF
  generatePurchaseOrderPdf: async (id: string): Promise<Blob> => {
    const response = await apiClient.get(`/purchase-orders/${id}/pdf`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Print purchase order
  printPurchaseOrder: async (id: string): Promise<Blob> => {
    const response = await apiClient.get(`/purchase-orders/${id}/print`, {
      responseType: 'blob',
    });
    return response.data;
  },
};
